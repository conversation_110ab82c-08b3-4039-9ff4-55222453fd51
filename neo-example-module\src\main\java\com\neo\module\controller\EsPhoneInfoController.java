package com.neo.module.controller;

import com.neo.model.Result;
import com.neo.module.commom.EsBaseController;
import com.neo.module.es.EsPhoneInfoMapper;
import com.neo.module.entity.EsPhoneInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.kernel.EsWrappers;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * ES手机信息控制器
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
@RestController
@RequestMapping("/es/phoneInfo")
public class EsPhoneInfoController extends EsBaseController {

    @Resource
    private EsPhoneInfoMapper esPhoneInfoMapper;

    /**
     * 分页查询手机信息
     *
     * @param params 查询参数
     * @return 分页结果
     */
    @PostMapping("/page")
    public Result<?> page(@RequestBody Map<String, String> params) {
        LambdaEsQueryWrapper<EsPhoneInfo> wrapper = buildQueryWrapper(params);

        int current = this.getCurrentPage(params);
        int pageSize = this.getPageSize(params);
        
        return Result.ok(esPhoneInfoMapper.pageQuery(wrapper, current, pageSize));
    }

    /**
     * 查询手机信息列表
     *
     * @param params 查询参数
     * @return 查询结果
     */
    @PostMapping("/list")
    public Result<?> list(@RequestBody Map<String, String> params) {
        LambdaEsQueryWrapper<EsPhoneInfo> wrapper = buildQueryWrapper(params);
        List<EsPhoneInfo> list = esPhoneInfoMapper.selectList(wrapper);
        return Result.ok(list);
    }

    /**
     * 根据ID查询手机信息
     *
     * @param id 手机ID
     * @return 手机信息
     */
    @GetMapping("/{id}")
    public Result<?> get(@PathVariable String id) {
        EsPhoneInfo phoneInfo = esPhoneInfoMapper.selectById(id);
        return Result.ok(phoneInfo);
    }

    /**
     * 新增手机信息
     *
     * @param esPhoneInfo 手机信息
     * @return 操作结果
     */
    @PostMapping("/add")
    public Result<?> add(@RequestBody EsPhoneInfo esPhoneInfo) {
        esPhoneInfoMapper.insert(esPhoneInfo);
        return Result.ok();
    }

    /**
     * 批量新增手机信息
     *
     * @param phoneInfoList 手机信息列表
     * @return 操作结果
     */
    @PostMapping("/batchAdd")
    public Result<?> batchAdd(@RequestBody List<EsPhoneInfo> phoneInfoList) {
        esPhoneInfoMapper.insertBatch(phoneInfoList);
        return Result.ok();
    }

    /**
     * 更新手机信息
     *
     * @param esPhoneInfo 手机信息
     * @return 操作结果
     */
    @PutMapping("/update")
    public Result<?> update(@RequestBody EsPhoneInfo esPhoneInfo) {
        esPhoneInfoMapper.updateById(esPhoneInfo);
        return Result.ok();
    }

    /**
     * 删除手机信息
     *
     * @param ids 手机ID集合
     * @return 操作结果
     */
    @DeleteMapping
    public Result<?> delete(@RequestBody Set<String> ids) {
        esPhoneInfoMapper.deleteBatchIds(ids);
        return Result.ok();
    }

    /**
     * 创建索引
     *
     * @return 操作结果
     */
    @PostMapping("/createIndex")
    public Result<?> createIndex() {
        boolean success = esPhoneInfoMapper.createIndex();
        return success ? Result.ok("索引创建成功") : Result.fail("索引创建失败");
    }

    /**
     * 删除索引
     *
     * @return 操作结果
     */
    @PostMapping("/deleteIndex")
    public Result<?> deleteIndex() {
        boolean success = esPhoneInfoMapper.deleteIndex();
        return success ? Result.ok("索引删除成功") : Result.fail("索引删除失败");
    }

    /**
     * 多字段搜索
     *
     * @param keyword 搜索关键词
     * @param params 其他查询参数
     * @return 搜索结果
     */
    @PostMapping("/search")
    public Result<?> search(@RequestParam String keyword, @RequestBody(required = false) Map<String, String> params) {
        LambdaEsQueryWrapper<EsPhoneInfo> wrapper = EsWrappers.lambdaQuery(EsPhoneInfo.class);
        
        if (StringUtils.hasText(keyword)) {
            wrapper.multiMatchQuery(keyword, EsPhoneInfo::getBrand, EsPhoneInfo::getModel, EsPhoneInfo::getCpu);
        }
        
        // 添加其他过滤条件
        if (params != null) {
            addFilterConditions(wrapper, params);
        }
        
        int current = this.getCurrentPage(params);
        int pageSize = this.getPageSize(params);
        
        return Result.ok(esPhoneInfoMapper.pageQuery(wrapper, current, pageSize));
    }

    /**
     * 构建查询条件
     *
     * @param params 查询参数
     * @return 查询包装器
     */
    private LambdaEsQueryWrapper<EsPhoneInfo> buildQueryWrapper(Map<String, String> params) {
        LambdaEsQueryWrapper<EsPhoneInfo> wrapper = EsWrappers.lambdaQuery(EsPhoneInfo.class);
        
        if (params == null || params.isEmpty()) {
            return wrapper;
        }

        // 品牌精确匹配
        String brand = params.get("brand");
        if (StringUtils.hasText(brand)) {
            wrapper.eq(EsPhoneInfo::getBrand, brand);
        }

        // 型号模糊匹配
        String model = params.get("model");
        if (StringUtils.hasText(model)) {
            wrapper.match(EsPhoneInfo::getModel, model);
        }

        // CPU模糊匹配
        String cpu = params.get("cpu");
        if (StringUtils.hasText(cpu)) {
            wrapper.match(EsPhoneInfo::getCpu, cpu);
        }

        // 添加其他过滤条件
        addFilterConditions(wrapper, params);

        return wrapper;
    }

    /**
     * 添加过滤条件
     *
     * @param wrapper 查询包装器
     * @param params 查询参数
     */
    private void addFilterConditions(LambdaEsQueryWrapper<EsPhoneInfo> wrapper, Map<String, String> params) {
        // 内存大小范围查询
        String minMemory = params.get("minMemorySize");
        String maxMemory = params.get("maxMemorySize");
        if (StringUtils.hasText(minMemory)) {
            wrapper.ge(EsPhoneInfo::getMemorySize, Integer.parseInt(minMemory));
        }
        if (StringUtils.hasText(maxMemory)) {
            wrapper.le(EsPhoneInfo::getMemorySize, Integer.parseInt(maxMemory));
        }

        // 存储大小范围查询
        String minStorage = params.get("minStorageSize");
        String maxStorage = params.get("maxStorageSize");
        if (StringUtils.hasText(minStorage)) {
            wrapper.ge(EsPhoneInfo::getStorageSize, Integer.parseInt(minStorage));
        }
        if (StringUtils.hasText(maxStorage)) {
            wrapper.le(EsPhoneInfo::getStorageSize, Integer.parseInt(maxStorage));
        }

        // 价格范围查询
        String minPrice = params.get("minPrice");
        String maxPrice = params.get("maxPrice");
        if (StringUtils.hasText(minPrice)) {
            wrapper.ge(EsPhoneInfo::getPrice, new BigDecimal(minPrice));
        }
        if (StringUtils.hasText(maxPrice)) {
            wrapper.le(EsPhoneInfo::getPrice, new BigDecimal(maxPrice));
        }

        // 操作系统类型
        String osType = params.get("osType");
        if (StringUtils.hasText(osType)) {
            wrapper.eq(EsPhoneInfo::getOsType, osType);
        }

        // 网络类型
        String networkType = params.get("networkType");
        if (StringUtils.hasText(networkType)) {
            wrapper.eq(EsPhoneInfo::getNetworkType, networkType);
        }

        // 是否支持5G
        String is5gSupported = params.get("is5gSupported");
        if (StringUtils.hasText(is5gSupported)) {
            wrapper.eq(EsPhoneInfo::getIs5gSupported, is5gSupported);
        }

        // 发布日期范围查询
        String startDate = params.get("startReleaseDate");
        String endDate = params.get("endReleaseDate");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (StringUtils.hasText(startDate)) {
            wrapper.ge(EsPhoneInfo::getReleaseDate, LocalDate.parse(startDate, formatter));
        }
        if (StringUtils.hasText(endDate)) {
            wrapper.le(EsPhoneInfo::getReleaseDate, LocalDate.parse(endDate, formatter));
        }

        // 颜色
        String color = params.get("color");
        if (StringUtils.hasText(color)) {
            wrapper.eq(EsPhoneInfo::getColor, color);
        }
    }

}
