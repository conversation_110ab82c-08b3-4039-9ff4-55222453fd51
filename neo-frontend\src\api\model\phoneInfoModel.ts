/**
 * 手机信息数据模型
 */

/**
 * 手机信息实体
 */
export interface PhoneInfo {
  id?: string;
  brand?: string;
  model?: string;
  cpu?: string;
  memorySize?: number;
  storageSize?: number;
  screenSize?: number;
  screenResolution?: string;
  cameraMain?: number;
  cameraFront?: number;
  batteryCapacity?: number;
  osType?: string;
  osVersion?: string;
  price?: number;
  releaseDate?: string;
  networkType?: string;
  weight?: number;
  color?: string;
  is5gSupported?: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
}

/**
 * 手机信息分页查询参数
 */
export interface PhoneInfoPageParams {
  pageNum: number;
  pageSize: number;
  brand?: string;
  model?: string;
  osType?: string;
  minPrice?: number;
  maxPrice?: number;
  networkType?: string;
  color?: string;
  is5gSupported?: string;
}

/**
 * 手机信息列表结果
 */
export interface PhoneInfoListResult {
  records: PhoneInfo[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 手机信息分页结果
 */
export type PhoneInfoPageResult = PhoneInfoListResult;