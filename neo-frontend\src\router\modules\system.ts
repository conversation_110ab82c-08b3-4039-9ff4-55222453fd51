import Layout from '@/layouts/index.vue';

export default [
  {
    path: '/system',
    name: 'system',
    component: Layout,
    redirect: '/system/user',
    meta: { title: { zh_CN: '系统管理', en_US: 'System Management' }, icon: 'setting' },
    children: [
      {
        path: 'user',
        name: 'SystemUser',
        component: () => import('@/pages/system/user/index.vue'),
        meta: { title: { zh_CN: '用户管理', en_US: 'User Management' } },
      },
      {
        path: 'dept',
        name: 'SystemDept',
        component: () => import('@/pages/system/dept/index.vue'),
        meta: { title: { zh_CN: '部门管理', en_US: 'Department Management' } },
      },
      {
        path: 'role',
        name: 'SystemRole',
        component: () => import('@/pages/system/role/index.vue'),
        meta: { title: { zh_CN: '角色管理', en_US: 'Role Management' } },
      },
      {
        path: 'menu',
        name: 'SystemMenu',
        component: () => import('@/pages/system/menu/index.vue'),
        meta: { title: { zh_CN: '菜单管理', en_US: 'Menu Management' } },
      },
      {
        path: 'post',
        name: 'SystemPost',
        component: () => import('@/pages/system/post/index.vue'),
        meta: { title: { zh_CN: '岗位管理', en_US: 'Post Management' } },
      },
      {
        path: 'dict',
        name: 'SystemDict',
        component: () => import('@/pages/system/dict/index.vue'),
        meta: { title: { zh_CN: '字典管理', en_US: 'Dictionary Management' } },
      },
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('@/pages/system/config/index.vue'),
        meta: { title: { zh_CN: '系统配置', en_US: 'System Configuration' } },
      },
      {
        path: 'log',
        name: 'SystemLog',
        component: () => import('@/pages/system/log/index.vue'),
        meta: { title: { zh_CN: '系统日志', en_US: 'System Log' } },
      },
    ],
  },
];