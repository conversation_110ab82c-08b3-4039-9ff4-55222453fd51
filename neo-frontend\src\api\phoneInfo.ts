import { request } from '@/utils/request';
import { PhoneInfo, PhoneInfoListResult, PhoneInfoPageParams, PhoneInfoPageResult } from './model/phoneInfoModel';

const Api = {
  PhoneInfoList: '/example/neoPhoneInfo/list',
  PhoneInfoPage: '/example/neoPhoneInfo/page',
  PhoneInfoAdd: '/example/neoPhoneInfo/add',
  PhoneInfoUpdate: '/example/neoPhoneInfo/update',
  PhoneInfoDelete: '/example/neoPhoneInfo',
  PhoneInfoGet: '/example/neoPhoneInfo/',
};

/**
 * 获取手机信息列表
 * @param params 查询参数
 */
export function getPhoneInfoList(params: PhoneInfo) {
  return request.post<PhoneInfoListResult>({
    url: Api.PhoneInfoList,
    data: params,
  });
}

/**
 * 分页查询手机信息
 * @param params 分页查询参数
 */
export function getPhoneInfoPage(params: PhoneInfoPageParams) {
  return request.post<PhoneInfoPageResult>({
    url: Api.PhoneInfoPage,
    data: params,
  });
}

/**
 * 根据ID获取手机信息
 * @param id 手机信息ID
 */
export function getPhoneInfoById(id: string) {
  return request.get<PhoneInfo>({
    url: `${Api.PhoneInfoGet}${id}`,
  });
}

/**
 * 新增手机信息
 * @param data 手机信息数据
 */
export function addPhoneInfo(data: PhoneInfo) {
  return request.post({
    url: Api.PhoneInfoAdd,
    data,
  });
}

/**
 * 更新手机信息
 * @param data 手机信息数据
 */
export function updatePhoneInfo(data: PhoneInfo) {
  return request.put({
    url: Api.PhoneInfoUpdate,
    data,
  });
}

/**
 * 删除手机信息
 * @param ids 手机信息ID数组
 */
export function deletePhoneInfo(ids: string[]) {
  return request.delete({
    url: Api.PhoneInfoDelete,
    data: ids,
  });
}