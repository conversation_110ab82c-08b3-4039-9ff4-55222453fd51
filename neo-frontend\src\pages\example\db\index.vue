<template>
  <div class="phone-management-container">
    <!-- 筛选表单 -->
    <t-card class="filter-card" :bordered="false">
      <template #actions>
        <t-button theme="default" variant="text" @click="isExpanded = !isExpanded">
          <template #icon>
            <t-icon :name="isExpanded ? 'chevron-up' : 'chevron-down'" />
          </template>
          {{ isExpanded ? '收起' : '展开' }}
        </t-button>
      </template>
      <t-form v-show="isExpanded" :data="searchForm" :label-width="80" colon @reset="resetSearch" @submit="onSearchSubmit">
        <t-row :gutter="[32, 24]">
          <t-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <t-form-item label="手机品牌" name="brand">
              <t-input
                v-model="searchForm.brand"
                class="form-item-content"
                type="search"
                placeholder="请输入手机品牌"
                clearable
              />
            </t-form-item>
          </t-col>
          <t-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <t-form-item label="手机型号" name="model">
              <t-input
                v-model="searchForm.model"
                class="form-item-content"
                placeholder="请输入手机型号"
                clearable
              />
            </t-form-item>
          </t-col>
          <t-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <t-form-item label="系统类型" name="osType">
              <t-select
                v-model="searchForm.osType"
                class="form-item-content"
                placeholder="请选择系统类型"
                clearable
              >
                <t-option key="Android" label="Android" value="Android" />
                <t-option key="iOS" label="iOS" value="iOS" />
                <t-option key="HarmonyOS" label="HarmonyOS" value="HarmonyOS" />
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <t-form-item label="网络类型" name="networkType">
              <t-select
                v-model="searchForm.networkType"
                class="form-item-content"
                placeholder="请选择网络类型"
                clearable
              >
                <t-option key="4G" label="4G" value="4G" />
                <t-option key="5G" label="5G" value="5G" />
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <t-form-item label="颜色" name="color">
              <t-select
                v-model="searchForm.color"
                class="form-item-content"
                placeholder="请选择颜色"
                clearable
              >
                <t-option key="黑色" label="黑色" value="黑色" />
                <t-option key="白色" label="白色" value="白色" />
                <t-option key="蓝色" label="蓝色" value="蓝色" />
                <t-option key="红色" label="红色" value="红色" />
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <t-form-item label="价格范围" name="priceRange">
              <div class="price-range">
                <t-input-number v-model="searchForm.minPrice" placeholder="最低价格" :step="100" class="price-input" />
                <span class="price-separator">-</span>
                <t-input-number v-model="searchForm.maxPrice" placeholder="最高价格" :step="100" class="price-input" />
              </div>
            </t-form-item>
          </t-col>
          <t-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="form-actions-container">
            <t-space size="medium">
              <t-button theme="primary" type="submit">
                <template #icon>
                  <t-icon name="search" />
                </template>
                查询
              </t-button>
              <t-button type="reset" variant="base" theme="default">
                <template #icon>
                  <t-icon name="refresh" />
                </template>
                重置
              </t-button>
            </t-space>
          </t-col>
        </t-row>
      </t-form>
    </t-card>

    <!-- 表格容器 -->
    <t-card class="table-card" :bordered="false">
      <div class="table-header">
        <div class="left-operation-container">
          <t-button @click="handleAdd">
            <template #icon>
              <t-icon name="add" />
            </template>
            新增手机
          </t-button>
        </div>
      </div>

      <t-table
        :data="data"
        :columns="COLUMNS"
        :row-key="'id'"
        vertical-align="top"
        :hover="true"
        :pagination="pagination"
        :loading="dataLoading"
        @page-change="rehandlePageChange"
        @change="rehandleChange"
        @select-change="(value: (string | number)[]) => rehandleSelectChange(value)"
      >
        <template #brand="{ row }">
          <div class="brand-cell">
            <t-avatar shape="round" size="small">{{ row.brand?.charAt(0) }}</t-avatar>
            <span class="brand-name">{{ row.brand }}</span>
          </div>
        </template>
        
        <template #is5gSupported="{ row }">
          <t-tag v-if="row.is5gSupported === 'Y'" theme="success" variant="light">
            支持
          </t-tag>
          <t-tag v-if="row.is5gSupported === 'N'" theme="warning" variant="light">
            不支持
          </t-tag>
        </template>

        <template #op="slotProps">
          <t-space>
            <t-link theme="primary" @click="handleEdit(slotProps.row)">
              <t-icon name="edit" /> 编辑
            </t-link>
            <t-popconfirm 
              content="确定要删除吗？" 
              @confirm="handleDelete(slotProps.row)"
            >
              <t-link theme="danger">
                <t-icon name="delete" /> 删除
              </t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 手机信息编辑弹窗 -->
    <t-dialog
      v-model:visible="formVisible"
      :header="isEdit ? '编辑手机信息' : '新增手机信息'"
      width="700px"
      :footer="false"
      class="phone-form-dialog"
    >
      <template #body>
        <t-form
          ref="form"
          :data="formData"
          :rules="rules"
          :label-width="80"
          @submit="onFormSubmit"
        >
          <t-row :gutter="[16, 16]">
            <t-col :span="12">
              <t-form-item label="手机品牌" name="brand">
                <t-input v-model="formData.brand" placeholder="请输入手机品牌" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="手机型号" name="model">
                <t-input v-model="formData.model" placeholder="请输入手机型号" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="处理器" name="cpu">
                <t-input v-model="formData.cpu" placeholder="请输入处理器型号" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="内存(GB)" name="memorySize">
                <t-input-number v-model="formData.memorySize" placeholder="请输入内存大小" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="存储(GB)" name="storageSize">
                <t-input-number v-model="formData.storageSize" placeholder="请输入存储大小" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="屏幕尺寸" name="screenSize">
                <t-input-number v-model="formData.screenSize" placeholder="请输入屏幕尺寸" :step="0.1" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="分辨率" name="screenResolution">
                <t-input v-model="formData.screenResolution" placeholder="请输入屏幕分辨率" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="主摄像头" name="cameraMain">
                <t-input-number v-model="formData.cameraMain" placeholder="请输入主摄像头像素" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="前摄像头" name="cameraFront">
                <t-input-number v-model="formData.cameraFront" placeholder="请输入前摄像头像素" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="电池容量" name="batteryCapacity">
                <t-input-number v-model="formData.batteryCapacity" placeholder="请输入电池容量" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="系统类型" name="osType">
                <t-select v-model="formData.osType" placeholder="请选择系统类型">
                  <t-option key="Android" label="Android" value="Android" />
                  <t-option key="iOS" label="iOS" value="iOS" />
                  <t-option key="HarmonyOS" label="HarmonyOS" value="HarmonyOS" />
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="系统版本" name="osVersion">
                <t-input v-model="formData.osVersion" placeholder="请输入系统版本" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="价格" name="price">
                <t-input-number v-model="formData.price" placeholder="请输入价格" :step="0.01" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="发布日期" name="releaseDate">
                <t-date-picker v-model="formData.releaseDate" mode="date" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="网络类型" name="networkType">
                <t-select v-model="formData.networkType" placeholder="请选择网络类型">
                  <t-option key="4G" label="4G" value="4G" />
                  <t-option key="5G" label="5G" value="5G" />
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="重量(g)" name="weight">
                <t-input-number v-model="formData.weight" placeholder="请输入重量" :step="0.1" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="颜色" name="color">
                <t-select v-model="formData.color" placeholder="请选择颜色">
                  <t-option key="黑色" label="黑色" value="黑色" />
                  <t-option key="白色" label="白色" value="白色" />
                  <t-option key="蓝色" label="蓝色" value="蓝色" />
                  <t-option key="红色" label="红色" value="红色" />
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="支持5G" name="is5gSupported">
                <t-radio-group v-model="formData.is5gSupported">
                  <t-radio value="Y">支持</t-radio>
                  <t-radio value="N">不支持</t-radio>
                </t-radio-group>
              </t-form-item>
            </t-col>
          </t-row>
          <t-form-item class="form-actions">
            <t-space>
              <t-button theme="primary" type="submit">
                <template #icon>
                  <t-icon name="check" />
                </template>
                提交
              </t-button>
              <t-button theme="default" variant="base" @click="onReset">
                <template #icon>
                  <t-icon name="refresh" />
                </template>
                重置
              </t-button>
              <t-button theme="default" variant="base" @click="formVisible = false">
                <template #icon>
                  <t-icon name="close" />
                </template>
                取消
              </t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <t-dialog
      v-model:visible="confirmVisible"
      header="确认删除"
      :body="confirmBody"
      :on-cancel="onCancelDelete"
      @confirm="onConfirmDelete"
    />
  </div>
</template>

<style lang="less" scoped>
.phone-management-container {
  background-color: var(--td-bg-color-container);
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
  border-radius: var(--td-radius-medium);
  
  .filter-card {
    margin-bottom: var(--td-comp-margin-xxl);
  }
  
  .table-card {
    .table-header {
      margin-bottom: var(--td-comp-margin-xl);
      
      .left-operation-container {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }
  }

  .brand-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .brand-name {
      font-weight: 500;
    }
  }

  .price-range {
    display: flex;
    align-items: center;
    
    .price-input {
      width: 120px;
    }
    
    .price-separator {
      margin: 0 8px;
    }
  }
}

.form-item-content {
  width: 100%;
}

.operation-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
}

.form-actions {
  margin-top: 16px;
  text-align: right;
}

.form-actions-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

.price-range {
  display: flex;
  align-items: center;
  
  .price-input {
    width: 100px;
  }
  
  .price-separator {
    margin: 0 8px;
  }
}

.phone-form-dialog {
  :deep(.t-form__item) {
    margin-bottom: 16px;
  }
}
</style>

<script setup lang="ts">
import { SearchIcon } from 'tdesign-icons-vue-next';
import type { PrimaryTableCol, TableRowData } from 'tdesign-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, onMounted, ref, reactive } from 'vue';

import { usePagination } from '@/hooks';
import { PhoneInfo, PhoneInfoPageParams } from '@/api/model/phoneInfoModel';

defineOptions({
  name: 'PhoneInfoList',
});

// 导入API请求函数
import { 
  getPhoneInfoPage, 
  getPhoneInfoById, 
  addPhoneInfo, 
  updatePhoneInfo, 
  deletePhoneInfo 
} from '@/api/phoneInfo';
// API请求函数已从phoneInfo.ts导入

// 表格列定义
const COLUMNS: PrimaryTableCol<TableRowData>[] = [
  { colKey: 'row-select', type: 'multiple', width: 64, fixed: 'left' },
  {
    title: '手机品牌',
    align: 'left',
    width: 120,
    colKey: 'brand',
    fixed: 'left',
  },
  {
    title: '手机型号',
    width: 120,
    colKey: 'model'
  },
  {
    title: '处理器',
    width: 120,
    colKey: 'cpu'
  },
  {
    title: '内存(GB)',
    width: 100,
    colKey: 'memorySize'
  },
  {
    title: '存储(GB)',
    width: 100,
    colKey: 'storageSize'
  },
  {
    title: '屏幕尺寸',
    width: 100,
    colKey: 'screenSize'
  },
  {
    title: '价格',
    width: 100,
    colKey: 'price'
  },
  {
    title: '发布日期',
    width: 120,
    colKey: 'releaseDate'
  },
  {
    title: '支持5G',
    width: 100,
    colKey: 'is5gSupported'
  },
  {
    title: '操作',
    align: 'left',
    fixed: 'right',
    width: 160,
    colKey: 'op',
  },
];

const selectedRowKeys = ref<(string | number)[]>([]);

// 搜索表单数据
const isExpanded = ref(true);

const searchForm = ref<{
  brand: string;
  model: string;
  osType: string | null;
  minPrice: number | null;
  maxPrice: number | null;
  networkType: string | null;
  color: string | null;
  is5gSupported: string | null;
}>({  
  brand: '',
  model: '',
  osType: null,
  minPrice: null,
  maxPrice: null,
  networkType: null,
  color: null,
  is5gSupported: null,
});

// 使用通用分页 hook
const paginationData = usePagination<PhoneInfo>({
  defaultCurrent: 1,
  defaultPageSize: 10,
});

// 将原来的 data 和 pagination 替换为通用分页数据
const data = paginationData.dataSource;
const pagination = paginationData.pagination;
const dataLoading = paginationData.loading;

// 数据加载函数
const loadPhoneData = async (params: PhoneInfoPageParams): Promise<any> => {
  const res = await getPhoneInfoPage(params);
  return {
    records: res.records,
    total: res.total,
    current: res.current,
    size: res.size,
  };
};

// 搜索提交
const onSearchSubmit = ({ validateResult }: { validateResult: boolean }) => {
  if (validateResult === true) {
    fetchData();
  }
};

// 重置搜索条件
const resetSearch = () => {
  searchForm.value = {
    brand: '',
    model: '',
    osType: null,
    minPrice: null,
    maxPrice: null,
    networkType: null,
    color: null,
    is5gSupported: null,
  };
  fetchData();
};

const fetchData = () => {
  const params: PhoneInfoPageParams = {
    pageNum: 1,
    pageSize: 10,
    brand: searchForm.value.brand,
    model: searchForm.value.model,
    osType: searchForm.value.osType,
    minPrice: searchForm.value.minPrice || undefined,
    maxPrice: searchForm.value.maxPrice || undefined,
    networkType: searchForm.value.networkType,
    color: searchForm.value.color,
    is5gSupported: searchForm.value.is5gSupported,
  };
  paginationData.resetToFirstPage(loadPhoneData, params);
};

// 表格事件处理
const rehandlePageChange = (pageInfo: any) => {
  const params: PhoneInfoPageParams = {
    pageNum: pageInfo.current,
    pageSize: pageInfo.pageSize,
    brand: searchForm.value.brand,
    model: searchForm.value.model,
    osType: searchForm.value.osType,
    minPrice: searchForm.value.minPrice || undefined,
    maxPrice: searchForm.value.maxPrice || undefined,
    networkType: searchForm.value.networkType,
    color: searchForm.value.color,
    is5gSupported: searchForm.value.is5gSupported,
  };
  paginationData.handlePageChange(pageInfo, loadPhoneData, params);
};

const rehandleChange = (changeParams: unknown, triggerAndData: unknown) => {
  console.log('统一Change', changeParams, triggerAndData);
};

const rehandleSelectChange = (value: (string | number)[]) => {
  selectedRowKeys.value = value;
};

// 表单相关
const formVisible = ref(false);
const isEdit = ref(false);

// 表单重置方法
const onReset = () => {
  formData.value = {
    brand: '',
    model: '',
    cpu: '',
    memorySize: null,
    storageSize: null,
    screenSize: null,
    screenResolution: '',
    cameraMain: null,
    cameraFront: null,
    batteryCapacity: null,
    osType: '',
    osVersion: '',
    price: null,
    releaseDate: null,
    networkType: '',
    weight: null,
    color: '',
    is5gSupported: 'N',
  };
};

const formData = ref<PhoneInfo>({
  brand: '',
  model: '',
  cpu: '',
  memorySize: null,
  storageSize: null,
  screenSize: null,
  screenResolution: '',
  cameraMain: null,
  cameraFront: null,
  batteryCapacity: null,
  osType: '',
  osVersion: '',
  price: null,
  releaseDate: null,
  networkType: '',
  weight: null,
  color: '',
  is5gSupported: 'N',
});

const rules = {
  brand: [{ required: true, message: '请输入手机品牌', type: 'error' }],
  model: [{ required: true, message: '请输入手机型号', type: 'error' }],
};

const handleAddPhone = () => {
  isEdit.value = false;
  formData.value = {
    brand: '',
    model: '',
    cpu: '',
    memorySize: null,
    storageSize: null,
    screenSize: null,
    screenResolution: '',
    cameraMain: null,
    cameraFront: null,
    batteryCapacity: null,
    osType: '',
    osVersion: '',
    price: null,
    releaseDate: null,
    networkType: '',
    weight: null,
    color: '',
    is5gSupported: 'N',
  };
  formVisible.value = true;
};

const handleEdit = async (row: PhoneInfo) => {
  try {
    // 在打开弹窗前先获取手机详情
    const response = await getPhoneInfoById(String(row.id));
    const phoneDetail = response.data;
    
    // 设置编辑状态和手机数据
    isEdit.value = true;
    formData.value = phoneDetail;
    
    // 打开弹窗
    formVisible.value = true;
  } catch (e) {
    console.error('获取手机详情失败:', e);
    MessagePlugin.error('获取手机详情失败，请重试');
  }
};

const onFormSubmit = async ({ validateResult, firstError }: { validateResult: boolean, firstError: string }) => {
  if (validateResult === true) {
    try {
      if (isEdit.value) {
        await updatePhoneInfo(formData.value);
        MessagePlugin.success('更新成功');
      } else {
        await addPhoneInfo(formData.value);
        MessagePlugin.success('新增成功');
      }
      formVisible.value = false;
      fetchData();
    } catch (error) {
      console.error('提交失败:', error);
      MessagePlugin.error('提交失败，请重试');
    }
  } else {
    console.log('Validate Errors: ', firstError);
    MessagePlugin.warning(firstError);
  }
};

// 删除相关
const confirmVisible = ref(false);
const confirmBody = ref('');
const currentDeleteId = ref<string>('');

const handleDelete = (row: any) => {
  currentDeleteId.value = row.id;
  confirmBody.value = `确认删除手机 ${row.brand} ${row.model} 吗？`;
  confirmVisible.value = true;
};

const onCancelDelete = () => {
  confirmVisible.value = false;
};

const onConfirmDelete = async () => {
  try {
    await deletePhoneInfo([currentDeleteId.value]);
    MessagePlugin.success('删除成功');
    fetchData();
  } catch (error) {
    console.error('删除失败:', error);
    MessagePlugin.error('删除失败，请重试');
  } finally {
    confirmVisible.value = false;
  }
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.t-form-item {
  margin-right: 12px;
  margin-bottom: 12px;
}

.mx-2 {
  margin-left: 4px;
  margin-right: 4px;
}

.ml-2 {
  margin-left: 8px;
}

@media (max-width: 768px) {
  .t-form-item {
    margin-right: 8px;
    margin-bottom: 8px;
  }
}
</style>

<style lang="less" scoped>
.t-card {
  margin-bottom: 16px;
}
</style>