package com.neo.module.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * ES手机信息实体类
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
@Data
@EqualsAndHashCode
@IndexName(value = "neo_phone_info")
public class EsPhoneInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @IndexId(type = IdType.CUSTOMIZE)
    private String id;

    /**
     * 手机品牌
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String brand;

    /**
     * 手机型号
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String model;

    /**
     * 处理器型号
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String cpu;

    /**
     * 内存大小(GB)
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer memorySize;

    /**
     * 存储大小(GB)
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer storageSize;

    /**
     * 屏幕尺寸(英寸)
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal screenSize;

    /**
     * 屏幕分辨率
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String screenResolution;

    /**
     * 主摄像头像素(MP)
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer cameraMain;

    /**
     * 前摄像头像素(MP)
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer cameraFront;

    /**
     * 电池容量(mAh)
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer batteryCapacity;

    /**
     * 操作系统类型
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String osType;

    /**
     * 操作系统版本
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String osVersion;

    /**
     * 价格
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal price;

    /**
     * 发布日期
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd")
    private LocalDate releaseDate;

    /**
     * 网络类型
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String networkType;

    /**
     * 重量(g)
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal weight;

    /**
     * 颜色
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String color;

    /**
     * 是否支持5G(Y/N)
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String is5gSupported;

}
